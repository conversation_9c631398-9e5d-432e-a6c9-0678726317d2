diff --git a/cmd/thinfluxd/run/server.go b/cmd/thinfluxd/run/server.go
index 934157e..7e8916f 100644
--- a/cmd/thinfluxd/run/server.go
+++ b/cmd/thinfluxd/run/server.go
@@ -175,6 +175,9 @@ func NewServer(c *Config, buildInfo *BuildInfo) (*Server, error) {
 	s.TSDBStore = tsdb.NewStore(c.Data.Dir)
 	s.TSDBStore.EngineOptions.Config = c.Data
 
+	// Apply ReadCache configuration correction
+	c.Data.ReadCache.Corrector()
+
 	// Copy TSDB configuration.
 	s.TSDBStore.EngineOptions.EngineVersion = c.Data.Engine
 	s.TSDBStore.EngineOptions.IndexVersion = c.Data.Index
diff --git a/tsdb/engine/tsm1/engine.go b/tsdb/engine/tsm1/engine.go
index b269233..1d7b4b2 100644
--- a/tsdb/engine/tsm1/engine.go
+++ b/tsdb/engine/tsm1/engine.go
@@ -35,6 +35,7 @@ import (
 	_ "github.com/influxdata/influxdb/tsdb/index"
 	"github.com/influxdata/influxdb/tsdb/index/inmem"
 	"github.com/influxdata/influxdb/tsdb/index/tsi1"
+	"github.com/influxdata/influxdb/tsdb/readcache"
 	"github.com/influxdata/influxql"
 	"go.uber.org/zap"
 )
@@ -274,6 +275,12 @@ func NewEngine(id uint64, idx tsdb.Index, path string, walPath string, sfile *ts
 		}
 	}
 
+	// Apply ReadCache configuration
+	readcache.SetPageSizeByConf(opt.Config.ReadCache.ReadPageSize)
+	readcache.SetMetaPageListByConf(opt.Config.ReadCache.ReadMetaPageSize)
+	readcache.EnableReadMetaCache(uint64(opt.Config.ReadCache.ReadMetaCacheEn))
+	readcache.EnableReadDataCache(uint64(opt.Config.ReadCache.ReadDataCacheEn))
+
 	return e
 }
 
diff --git a/tsdb/engine/tsm1/reader.go b/tsdb/engine/tsm1/reader.go
index 0fc840f..560bce4 100644
--- a/tsdb/engine/tsm1/reader.go
+++ b/tsdb/engine/tsm1/reader.go
@@ -367,6 +367,16 @@ func (t *TSMReader) Close() error {
 	t.mu.Lock()
 	defer t.mu.Unlock()
 
+	// Clean up cache entries when closing TSM file
+	if readcache.ReadMetaCacheEn {
+		cacheIns := readcache.GetReadMetaCacheIns()
+		cacheIns.Remove(t.Path())
+	}
+	if readcache.ReadDataCacheEn {
+		dataCacheIns := readcache.GetReadDataCacheIns()
+		dataCacheIns.RemovePageCache(t.Path())
+	}
+
 	if err := t.accessor.close(); err != nil {
 		return err
 	}
diff --git a/tsdb/readcache/blockcache.go b/tsdb/readcache/blockcache.go
index a825afd..a9a8e8b 100644
--- a/tsdb/readcache/blockcache.go
+++ b/tsdb/readcache/blockcache.go
@@ -189,18 +189,17 @@ func (c *blockCache) removePageCache(filePath string) bool {
 // Go 1.16 compatible implementation equivalent to openGemini's unsafe.Slice(unsafe.StringData(s), len(s))
 // Uses reflect.StringHeader and reflect.SliceHeader for safe zero-copy conversion
 func str2bytes(s string) []byte {
-	// 将字符串s的指针转换为 reflect.StringHeader 指针
+	// Convert string pointer to StringHeader
 	stringHeader := (*reflect.StringHeader)(unsafe.Pointer(&s))
 
-	// 创建一个 reflect.SliceHeader，并手动设置其 Data, Len, 和 Cap
-	// 让它直接使用字符串的底层数据
+	// Create SliceHeader using string's underlying data
 	sliceHeader := reflect.SliceHeader{
 		Data: stringHeader.Data,
 		Len:  stringHeader.Len,
 		Cap:  stringHeader.Len,
 	}
 
-	// 将伪造的 sliceHeader 的指针转换为 *[]byte 指针，然后解引用得到 []byte
+	// Convert SliceHeader to []byte
 	return *(*[]byte)(unsafe.Pointer(&sliceHeader))
 }
 
diff --git a/tsdb/readcache/readcache.go b/tsdb/readcache/readcache.go
index b57c7c1..2dbd7e3 100644
--- a/tsdb/readcache/readcache.go
+++ b/tsdb/readcache/readcache.go
@@ -18,7 +18,13 @@ const (
 	DefaultReadMetaCachePercent = 3
 	DefaultReadDataCachePercent = 10
 	KB                          = 1024
-	maxMemUse                   = 64 * 1024 * 1024 * 1024
+	MB                          = 1024 * 1024
+	GB                          = 1024 * 1024 * 1024
+	maxMemUse                   = 64 * GB
+
+	// Memory limits for ReadCache configuration (openGemini-compatible)
+	MinMemoryLimit = 8 * GB   // 8GB minimum
+	MaxMemoryLimit = 512 * GB // 512GB maximum
 )
 
 var (
@@ -46,7 +52,7 @@ func NewReadCacheConfig() ReadCacheConfig {
 		ReadMetaPageSize:   []string{},
 		ReadMetaCacheEn:    toml.Size(1),
 		ReadMetaCacheEnPct: toml.Size(DefaultReadMetaCachePercent),
-		ReadDataCacheEn:    toml.Size(getReadMetaCacheLimitSize(uint64(memorySize))),
+		ReadDataCacheEn:    toml.Size(GetReadMetaCacheLimitSize(uint64(memorySize))),
 		ReadDataCacheEnPct: toml.Size(DefaultReadDataCachePercent),
 	}
 }
@@ -63,11 +69,11 @@ func SetReadDataCachePct(pct int) {
 	}
 }
 
-func getReadMetaCacheLimitSize(size uint64) uint64 {
+func GetReadMetaCacheLimitSize(size uint64) uint64 {
 	return size * uint64(ReadMetaCachePct) / 100
 }
 
-func getReadDataCacheLimitSize(size uint64) uint64 {
+func GetReadDataCacheLimitSize(size uint64) uint64 {
 	return size * uint64(ReadDataCachePct) / 100
 }
 
@@ -196,3 +202,62 @@ func IsObjectNil(obj interface{}) bool {
 
 	return false
 }
+
+// ReadMetaCacheEn controls whether metadata cache is enabled
+var ReadMetaCacheEn = false
+
+// ReadDataCacheEn controls whether data cache is enabled
+var ReadDataCacheEn = false
+
+// EnableReadMetaCache enables or disables the metadata cache based on the size limit
+func EnableReadMetaCache(en uint64) {
+	if en > 0 {
+		ReadMetaCacheEn = true
+		SetReadMetaCacheLimitSize(en)
+	} else {
+		ReadMetaCacheEn = false
+	}
+}
+
+// EnableReadDataCache enables or disables the data cache based on the size limit
+func EnableReadDataCache(en uint64) {
+	if en > 0 {
+		ReadDataCacheEn = true
+		SetReadDataCacheLimitSize(en)
+	} else {
+		ReadDataCacheEn = false
+	}
+}
+
+// Uint64Limit applies min/max limits to a uint64 value (openGemini-compatible)
+func Uint64Limit(min, max, value uint64) uint64 {
+	if value < min {
+		return min
+	}
+	if value > max {
+		return max
+	}
+	return value
+}
+
+// Corrector applies ReadCache configuration correction (openGemini-compatible)
+func (config *ReadCacheConfig) Corrector() {
+	// Get system memory for cache size calculations
+	memTotal, _ := SysMem()
+	memorySize := uint64(memTotal * KB)
+
+	// Set cache percentage parameters
+	SetReadMetaCachePct(int(config.ReadMetaCacheEnPct))
+	SetReadDataCachePct(int(config.ReadDataCacheEnPct))
+
+	// Apply memory limits using openGemini-compatible constants and helper function
+	limitedMemory := Uint64Limit(MinMemoryLimit, MaxMemoryLimit, memorySize)
+
+	// Recalculate cache sizes based on system memory and percentages
+	if config.ReadMetaCacheEn != 0 {
+		config.ReadMetaCacheEn = toml.Size(GetReadMetaCacheLimitSize(limitedMemory))
+	}
+	if config.ReadDataCacheEn != 0 {
+		config.ReadDataCacheEn = toml.Size(GetReadDataCacheLimitSize(limitedMemory))
+	}
+}
