[common]
  meta-join = ["{{meta_addr_1}}:8092", "{{meta_addr_2}}:8092", "{{meta_addr_3}}:8092"]
  # the shared storage-based store whether support HA.
  # write-available-first: if pt is mark offline, request will skip this pt
  # shared-storage: if pt is mark offline, request will retry until pt online
  # replication: request will retry until replication group has master
  # ha-policy = "write-available-first"
  # executor-memory-size-limit = "0"
  # executor-memory-wait-time = "0s"
  # cpu-num = 0
  # cpu-allocation-ratio = 1
  # memory-size = "0"
  # ignore-empty-tag = false
  # report-enable = true
  # node-role can be set to "reader", "writer". If no value is set, prioritize as writer, but if no reader in cluster, it is both "reader" and "writer".
  # node-role = ""
  # product-type can be left unset or set to "logkeeper".
  # product-type = ""

  ## Default value is true
  ## Set to false, the pre-aggregation information is not recorded in the metadata
  # pre-agg-enabled = true
  pprof-enabled = true


  ## global dictionary file list
  # global-dict-files = []

[meta]
  bind-address = "{{addr}}:8088"
  http-bind-address = "{{addr}}:8091"
  rpc-bind-address = "{{addr}}:8092"
  dir = "/tmp/openGemini/data/meta/{{id}}"
  #
  # expand-shards-enable = false
  # retention-autocreate = true
  # election-timeout = "1s"
  # heartbeat-timeout = "1s"
  # leader-lease-timeout = "500ms"
  # commit-timeout = "50ms"
  # cluster-tracing = true
  # logging-enabled = true
  # lease-duration = "1m0s"
  # meta-version = 0
  # split-row-threshold = 10000
  # imbalance-factor = 0.3
  # auth-enabled = false
  # https-enabled = false
  # https-certificate = ""
  # https-private-key = ""
  # ptnum-pernode = 1

  # Switch for serial balance and parallel balance
  # The default is "v1.1" of parallel balance, Serial balance is used only for setting "v1.0", Other settings use default parallel balance
  # balance-algorithm-version = "v1.1"
  # inc-sync-data = true
  # rep-dis-policy = 0

# [coordinator]
  # write-timeout = "10s"
  # shard-writer-timeout = "10s"
  # shard-mapper-timeout = "10s"
  # max-remote-write-connections = 100
  # max-remote-read-connections = 100
  # shard-tier = "warm"
  # rp-limit = 100
  # force-broadcast-query = false
  # time-range-limit = ["72h", "24h"]
  # tag-limit = 0
  ## Set the query timeout period.
  # query-timeout = "10s"
  # In WriteAvailableFirst mode, whether data is written to the ts-store that breaks down.
  # hard-write = true

[http]
  bind-address = "{{addr}}:8086"
  flight-address = "{{addr}}:8087"
  # flight-enabled = false
  # flight-ch-factor = 2
  # flight-auth-enabled = false
  # auth-enabled = false
  # weakpwd-path = "/tmp/openGemini/weakpasswd.properties"
  # max-connection-limit = 0
  # max-concurrent-write-limit = 0
  # max-enqueued-write-limit = 0
  # enqueued-write-timeout = "30s"
  # max-concurrent-query-limit = 0
  # max-enqueued-query-limit = 0
  # enqueued-query-timeout = "5m"
  # chunk-reader-parallel = 0
  # max-body-size = 0
  # https-enabled = false
  # https-certificate = ""
  # https-private-key = ""
  # time-filter-protection = false
  # parallel-query-in-batch-enabled = true
  # max-row-size-limit = 0
  # max-line-size = 65536
  #[http.result-cache]
  #  result-cache-enabled = true
  #  max-cache-freshness = "5m"
  #  cache-type = 0
  #  split-queries-by-interval = "15m"
  #  memcache-size = 102400
  #  memcache-expiration = "30s"

[data]
  store-ingest-addr = "{{addr}}:8400"
  store-select-addr = "{{addr}}:8401"
  store-data-dir = "/tmp/openGemini/data"
  store-wal-dir = "/tmp/openGemini/data"
  store-meta-dir = "/tmp/openGemini/data/meta/{{id}}"
  # Whether to use mmap ability
  enable-mmap-read = false
  # enable perl regrep method
  # enable-perl-regrep = false
  # write-concurrent-limit = 0
  # open-shard-limit = 0
  # readonly = false
  # downsample-write-drop = true
  # query will be estimated abd limited by resource manager
  # max-wait-resource-time = "0s"
  # max-series-parallelism-num = 0
  # max-shards-parallelism-num = 0
  # when create group cursor, the parallelism num will be estimated by resource allocator according to the chunk-reader-threshold and min-chunk-reader-concurrency
  # chunk-reader-threshold = 0
  # min-chunk-reader-concurrency = 0
  # minimum shards number for initializing shards in parallel
  # min-shards-concurrency = 0
  # max-downsample-task-concurrency defines the max downsample task num at the same time
  # max-downsample-task-concurrency = 0
  # maximum number of series a node can hold per database. 0: unlimited
  # max-series-per-database = 0
  # manage query file handle, default enable_query_file_handle_cache is true, default max_query_cached_file_handles is cpuNum*8
  # enable_query_file_handle_cache = true
  # if max_query_cached_file_handles is 0, default query_cached_file_handles is used
  # max_query_cached_file_handles = 0

  ## Determines whether the lazy shard open is enabled.
  # lazy-load-shard-enable = true

  ## The time range for thermal shards. If the duration is set to 0s, the default value is shard group duration of the first RP.
  # thermal-shard-start-duration = "0s"
  # thermal-shard-end-duration = "0s"

  ## If queries are auto killed for store service
  # interrupt-query = true
  ## The default store mem percent threshold of start killing query
  # interrupt-sql-mem-pct = 85
  ## The default time interval of checking store mem use
  # proactive-manager-interval = "100ms"

  ## Compresses temporary index files. 0: not compressed(default); 1: use snappy
  # temporary-index-compress-mode = 0

  ## Compressing ChunkMeta in TSSP Files.
  # 0: not compressed(default);
  # 1: use Snappy
  # 2: use LZ4
  # 3: self-encoded
  # chunk-meta-compress-mode = 0

  ## Indicates whether to persist the index read cache to disk when index close
  # index-read-cache-persistent = false

  ## compression algorithm used by data of the string type
  ## default value is snappy. Options: snappy, lz4, zstd
  # string-compress-algo = "snappy"

  ## Ordered data and unordered data are not distinguished. All data is processed as unordered data
  # unordered-only = false

  ## in some scenarios, it is allowed to write past time but ordered data(for examle, some scenarios allow to write the past 14 days data in order)
  # enable-write-history-ordered-data = false

  availability-zone = "az1"

  ## Tolerable duration for clearing entry logs if a node breaks down in replication mode. eg: 5h,10h,15h...Default is 10h
  # clear-entryLog-tolerate-time = "10h"

  ## Size that can be tolerated before the entry log is forcibly cleared.
  clear-entryLog-tolerate-size = "10G"

  ## Configuring Floating Point Numbers compression algorithm
  ## A empty value indicates the default algorithm.
  ## mlf: multiplication-based floating-point lossless compression algorithm
  # float-compress-algorithm = ""

  # [data.wal]
       # wal-enabled = true
       # wal-sync-interval = "100ms"
       # wal-replay-parallel = false
       # wal-replay-async = false
       # wal-replay-batch-size = "1m"

       # set to true: wal is used to ensure stream computing reliability
       # wal-used-for-stream = false
   # [data.memtable]
       # write-cold-duration = "5s"
       # force-snapShot-duration = "25s"
       # shard-mutable-size-limit = "60m"
       # node-mutable-size-limit = "200m"
       # max-write-hang-time = "15s"
       # mem-data-read-enabled = true
       # column-store-detached-flush-enabled = false
       # fragments-num-per-flush = 1
   # [data.compact]
       # compact-full-write-cold-duration = "1h"
       # max-concurrent-compactions = 4
       # max-full-compactions = 1
       # compact-throughput = "80m"
       # compact-throughput-burst = "90m"
       # snapshot-throughput = "64m"
       # snapshot-throughput-burst = "70m"
       # compact-recovery = false
       # column-store-compact-enabled = false
       # compaction-method = 0
       # Automatically corrects time disordered data.
       # When the value is true, compact is executed in non-streaming mode.
       # correct-time-disorder = false
       ## Upper limit of the compaction level. 0: not limited
       # max-compaction-level = 0
    [data.readcache]
       # If use read-meta-cache, default is 1. Equal to 0 is unused, default is 3% of memory size.
       enable-meta-cache = 9
       read-meta-page-size = ["4kb", "16kb"]
       # read-meta-cache-limit-pct = 3
       # If use read-data-cache, default is 0. Equal to 0 is unused, default is 10% of memory size
       # enable-data-cache = 0
       # read-data-cache-limit-pct = 10
       # read-page-size set pageSize of read from file of datablock, default is "32kb", valid setting is "1kb"/"4kb"/"8kb"/"16kb"/"32kb"/"64kb"/"variable"
       # read-page-size = "32kb"
       # read-meta-page-size set pageSize boundaries of meta hierarchical pool, default is nil which means do not enable hierarchical pool , valid item setting is "1kb"/"4kb"/"8kb"/"16kb"/"32kb"/"64kb"
       # read-meta-page-size = ["4kb", "16kb"]

[data.merge]
  # merge only unordered data
  # merge-self-only = false

  ## The number of unordered files to be merged each time cannot exceed MaxUnorderedFileNumber
  # max-unordered-file-number = 64
  ## The total size of unordered files to be merged each time cannot exceed MaxUnorderedFileSize
  # max-unordered-file-size = "8g"

  ## if the number of unordered files is small and
  ## no merging operation is performed within the interval
  ## merge the files forcibly
  # min-interval = "300s"

  ## Low-level files are merged self first
  # max-merge-self-level = 0

  ## high-level file merge-self using stream merge
  # stream-merge-mode-level = 2

[data.hot-mode]
  ## If this flag is set to true, the newly flushed file will be read into the memory.
  # enabled = false

  ## Allowed percent of system memory hot mode cache may occupy. default 5
  # memory-allowed-percent = 5

  ## The default value is 0, indicating that the time range is not limited
  # duration = "1h"

  ## When the memory usage reaches the threshold or hot data expires, hot data is converted to warm data in batches.
  ## Calculate the time window based on the maximum file time.
  ## Select the earliest time window and change the files in the window from hot to warn.
  # time-window = "60s"

  # Larger files are not cached
  # max-file-size = "2g"

  # the max object cnt of mem pool
  # pool-object-cnt = 2

  # the max local cache of mem pool
  # max-cache-size = "1g"

# [data.shelf-mode]
  # enabled = false

  ## Reliability requirements for data writing
  ## 1: Low reliability. Data may be lost due to process faults.
  ## 2: Medium reliability. Data may be lost due to container or VM faults. (default)
  ## 3: High reliability. Data may be lost when a storage medium is faulty.
  # reliability-level = 2

  # max-wal-file-size = "256m"
  # max-wal-duration = "300s"

  ## WAL data compression mode.
  ## 0: not compressed
  ## 1: LZ4 (default)
  ## 2: Snappy
  # wal-compress-mode = 1

  ## number of background write threads. default value is CPUNum/2
  # concurrent = 0

  ## by default, the table is grouped based on the hash value of the measurement name
  ## If this parameter is set to a value greater than 1,
  ## secondary grouping is performed based on the hash value of the series key
  # series-hash-factor = 1

  ## max number of concurrent WAL files to be converted to SSP files.
  ## default value is the same as Concurrent
  # tssp-convert-concurrent = 0

# [data.ops-monitor]
  # store-http-addr = "{{addr}}:8402"
  # auth-enabled = false
  # store-https-enabled = false
  # store-https-certificate = ""

[data.parquet-task]
  # enabled = false
  # tssp-to-parquet-level = 0 // for compaction

  ## group length of parquet file
  # max-group-len = 65535

  ##  Page size of parquet file
  # page-size = 65535

  ## parquet writer batch size
  # write-batch-size = 512

  ## parquet file storage directory
  # output-dir = "/data/openGemini/parquet_output"

  ## stores reliability logs for fault recovery
  # reliability-log-dir = "/data/openGemini/parquet_reliability_log"

# [retention]
  # enabled = true
  # check-interval = "30m"

# [downsample]
  # enable = true
  # check-interval = "30m"

# [index]
  # tsid-cache-size = 0            # default host.mem / 32
  # skey-cache-size = 0            # default host.mem / 32
  # tag-cache-size = 0             # default host.mem / 16
  # tag-filter-cost-cache-size = 0 # default host.mem / 128
  # index creation concurrency. default value is twice the number of CPU cores
  # concurrency = 0
  # bloom-filter-enable = false
  # tag-scan-prune-threshold = 0   # default 20000
  # Allowed percent of system memory VictoriaMetrics caches may occupy. default 60
  # memory-allowed-percent = 0

[logging]
  # format = "auto"
  # level = "info"
  path = "/tmp/openGemini/logs/{{id}}"
  # max-size = "64m"
  # max-num = 16
  # max-age = 7
  # compress-enabled = true

# [tls]
  # min-version = "TLS1.2"
  # ciphers = [
    # "TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256",
    # "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384",
    # "TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256",
    # "TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384",
  # ]

# [monitor]
  # pushers = ""
  # store-enabled = false
  # store-database = "_internal"
  # store-interval = "10s"
  # store-path = "/tmp/openGemini/metric/{{id}}/metric.data"
  # compress = false
  # https-enabled = false
  # http-endpoint = "127.0.0.1:8086"
  # username = ""
  # password = ""

[gossip]
  enabled = true
  log-enabled = true
  bind-address = "{{addr}}"
  store-bind-port = 8011
  meta-bind-port = 8010
  sql-bind-port = 8012
  # prob-interval = '400ms'
  # suspicion-mult = 4
  members = ["{{meta_addr_1}}:8010", "{{meta_addr_2}}:8010", "{{meta_addr_3}}:8010"]

# [spdy]
  # recv-window-size = 8
  # concurrent-accept-session = 4096
  # open-session-timeout = "2s"
  # session-select-timeout = "10s"
  # data-ack-timeout = "10s"
  # tcp-dial-timeout = "5s"
  # tls-enable = false
  # tls-insecure-skip-verify = false
  # tls-client-auth = false
  # tls-certificate = ""
  # tls-private-key = ""
  # tls-server-name = ""
  # conn-pool-size = 4
  # tls-client-certificate = ""
  # tls-client-private-key = ""
  # tls-ca-root = ""

# [castor]
  # enabled = false
  # pyworker-addr = ["127.0.0.1:6666"]  # format: ip:port
  # connect-pool-size = 30  # connection pool to pyworker
  # result-wait-timeout = 10  # unit: second
# [castor.detect]
  # algorithm = ['BatchDIFFERENTIATEAD','DIFFERENTIATEAD','IncrementalAD','ThresholdAD','ValueChangeAD']
  # config_filename = ['detect_base']
# [castor.fit_detect]
  # algorithm = ['BatchDIFFERENTIATEAD','DIFFERENTIATEAD','IncrementalAD','ThresholdAD','ValueChangeAD']
  # config_filename = ['detect_base']

# [sherlock]
  # sherlock-enable = false
  # collect-interval = "10s"
  # cpu-max-limit = 95
  # dump-path = "/tmp"
  # max-num = 32
  # max-age = 7
# [sherlock.cpu]
  # enable = false
  # min = 30
  # diff = 25
  # abs = 70
  # cool-down = "10m"
# [sherlock.memory]
  # enable = false
  # min = 25
  # diff = 25
  # abs = 80
  # cool-down = "10m"
# [sherlock.goroutine]
  # enable = false
  # min = 10000
  # diff = 20
  # abs = 20000
  # max = 100000
  # cool-down = "30m"

#[clv_config]
  # enabled = false
  # q-max is maximum token length of V-token(Variable Length Token) tokenizer.
  # q-max = 7
  # document-count indicates how many documents are collected for generating V-token tokenizer.
  # document-count = 500000
  # token-threshold indicates the pruning frequency of all tokens for the collected documents.
  # token-threshold = 100


[io-detector]
  # paths = []

[spec-limit]
  enable-query-when-exceed = true
  query-series-limit = 0
  query-schema-limit = 0

[subscriber]
  # enabled = false
  # http-timeout = "30s"
  # insecure-skip-verify = false
  # https-certificate = ""
  # write-buffer-size = 100
  # write-concurrency = 15

###
### [continuous_queries]
###
### Controls how continuous queries are run within openGemini.
###

[continuous_queries]
  ## Determines whether the continuous queries service is enabled.
  # enabled = true
  ## The interval for how often continuous queries will be checked if they need to run.
  # run-interval = "1s"
  ## concurrent exec continues queries goroutines number. Default 1/3 of cpu number, at least 1 and at most 5.
  # max-process-CQ-number = 0

[hierarchical_storage]
  ## If this flag is set to false, close  hierarchical storage service
  # enabled = false
  ## Run interval time for checking hierarchical storage.
  # run-interval= "1m"
  ## max process number for shard moving
  # max-process-HS-number =1

[runtime-config]
  enabled = false
  load-path = "/opt/dbs/runtimeconfig/overrides.yml"
  reload-period = "10s"

[limits]
  prom-limit-enabled = false
  max-label-name-length = 1024
  max-label-value-length = 2048
  max-label-names-per-series = 30
  max-metadata-length = 1024
  reject-old-samples = false
  reject-old-samples-max-age = "14d"
  creation-grace-period = "10m"
  enforce-metadata-metric-name = true
  enforce-metric-name = true
  max-query-length = "0"

###
### [record-write]
###
### Controls how write by record.Record are run within openGemini.

[record-write]
  ## Determines whether the record write service is enabled.
  # enabled = true
  ## Determines whether the username/password auth in record write service is enabled.
  # auth-enabled = false
  ## Use shelf write mode
  # shelf-mode = false
  ## The rpc bind address of record write service.
  # rpc-address = "{{addr}}:8305"
  ## The maximum message size of record write service counted in Bytes.
  ## By default, it is 4194304 Bytes.(4 MB)
  # max-message-size = 4194304

[record-write.TLS]
  ## Determines whether the TLS in record write service is enabled.
  ## If TLS is enabled, then key-file and cert-file MUST be provided.
  # enabled = false
  ## Determines whether the mutal-TLS in record write service is enabled.
  ## If mutual-TLS is enabled, then the CA-root MUST be provided.
  # mTLS-enabled = false
  ## The path to TLS key file.
  # key-file = ""
  ## The path to TLS cert file.
  # cert-file = ""
  ## The path to CA root file.
  # CA-root =""
