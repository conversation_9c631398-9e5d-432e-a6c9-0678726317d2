set confirm off
set pagination off

echo Setting key breakpoints...\n

# 1. Corrector function
break github.com/openGemini/openGemini/lib/config.(*Store).Corrector
commands
    echo \n=== Corrector Called ===\n
    echo Before correction:\n
    print this.ReadCache.ReadMetaCacheEn
    print this.ReadCache.ReadDataCacheEn
    continue
end

# 2. NewServer function  
break github.com/openGemini/openGemini/app/ts-store/run.NewServer
commands
    echo \n=== NewServer Called ===\n
    continue
end

# 3. OpenStorage function
break github.com/openGemini/openGemini/app/ts-store/storage.OpenStorage
commands
    echo \n=== OpenStorage Called ===\n
    continue
end

# 4. NewEngine function
break github.com/openGemini/openGemini/engine.NewEngine
commands
    echo \n=== NewEngine Called ===\n
    continue
end

# 5. EnableReadMetaCache
break github.com/openGemini/openGemini/lib/fileops.EnableReadMetaCache
commands
    echo \n=== EnableReadMetaCache Called ===\n
    print size
    continue
end

echo Starting program...\n
run --config config/openGemini.singlenode.conf
quit
