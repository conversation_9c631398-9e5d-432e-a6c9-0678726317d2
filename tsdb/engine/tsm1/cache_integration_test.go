package tsm1

import (
	"os"
	"testing"

	"github.com/influxdata/influxdb/tsdb/readcache"
)

// TestReadCacheIntegration verifies ReadCache integration with TSM file reading
func TestReadCacheIntegration(t *testing.T) {
	// Enable cache
	readcache.EnableReadDataCache(1024 * 1024) // 1MB
	defer readcache.EnableReadDataCache(0)     // Disable after test

	// 创建测试TSM文件
	dir := mustTempDir()
	defer os.RemoveAll(dir)
	f := mustTempFile(dir)
	defer f.Close()

	w, err := NewTSMWriter(f)
	if err != nil {
		t.Fatalf("unexpected error creating writer: %v", err)
	}

	// 写入测试数据
	values := []Value{NewValue(1, 1.1), NewValue(2, 2.2)}
	if err := w.Write([]byte("cpu"), values); err != nil {
		t.Fatalf("unexpected error writing: %v", err)
	}

	if err := w.WriteIndex(); err != nil {
		t.Fatalf("unexpected error writing index: %v", err)
	}

	if err := w.Close(); err != nil {
		t.Fatalf("unexpected error closing: %v", err)
	}

	// 创建TSMReader
	f.Seek(0, 0)
	r, err := NewTSMReader(f)
	if err != nil {
		t.Fatalf("unexpected error created reader: %v", err)
	}
	defer r.Close()

	// 第一次读取 - 应该从文件读取并缓存
	vals, err := r.ReadAll([]byte("cpu"))
	if err != nil {
		t.Fatalf("unexpected error reading: %v", err)
	}

	if len(vals) != 2 {
		t.Fatalf("expected 2 values, got %d", len(vals))
	}

	// 验证缓存实例存在
	cacheIns := readcache.GetReadDataCacheIns()
	if cacheIns == nil {
		t.Fatal("cache instance should not be nil")
	}

	// 第二次读取 - 应该从缓存读取
	vals2, err := r.ReadAll([]byte("cpu"))
	if err != nil {
		t.Fatalf("unexpected error reading from cache: %v", err)
	}

	if len(vals2) != 2 {
		t.Fatalf("expected 2 values from cache, got %d", len(vals2))
	}

	// 验证数据一致性
	for i := range vals {
		if vals[i].UnixNano() != vals2[i].UnixNano() {
			t.Fatalf("timestamp mismatch at index %d: %d != %d", i, vals[i].UnixNano(), vals2[i].UnixNano())
		}
		if vals[i].Value() != vals2[i].Value() {
			t.Fatalf("value mismatch at index %d: %v != %v", i, vals[i].Value(), vals2[i].Value())
		}
	}
}

// TestCacheKeyGeneration 验证缓存键生成与openGemini一致
func TestCacheKeyGeneration(t *testing.T) {
	cacheIns := readcache.GetReadDataCacheIns()

	// 使用相同的参数
	filePath := "/data/test.tsm"
	offset := int64(1024)

	key := cacheIns.CreateCacheKey(filePath, offset)
	expected := "/data/test.tsm&&1024" // openGemini的实际格式

	if key != expected {
		t.Errorf("Cache key format differs from openGemini: got %s, want %s", key, expected)
	}
}

// TestCacheDisabled verifies cache disabled behavior
func TestCacheDisabled(t *testing.T) {
	// Ensure cache is disabled
	readcache.EnableReadDataCache(0)
	defer readcache.EnableReadDataCache(0)

	if readcache.ReadDataCacheEn {
		t.Fatal("ReadDataCacheEn should be false when disabled")
	}

	// 创建测试TSM文件
	dir := mustTempDir()
	defer os.RemoveAll(dir)
	f := mustTempFile(dir)
	defer f.Close()

	w, err := NewTSMWriter(f)
	if err != nil {
		t.Fatalf("unexpected error creating writer: %v", err)
	}

	values := []Value{NewValue(1, 1.1)}
	if err := w.Write([]byte("cpu"), values); err != nil {
		t.Fatalf("unexpected error writing: %v", err)
	}

	if err := w.WriteIndex(); err != nil {
		t.Fatalf("unexpected error writing index: %v", err)
	}

	if err := w.Close(); err != nil {
		t.Fatalf("unexpected error closing: %v", err)
	}

	// 创建TSMReader并读取
	f.Seek(0, 0)
	r, err := NewTSMReader(f)
	if err != nil {
		t.Fatalf("unexpected error created reader: %v", err)
	}
	defer r.Close()

	// 读取应该正常工作（通过原有逻辑）
	vals, err := r.ReadAll([]byte("cpu"))
	if err != nil {
		t.Fatalf("unexpected error reading: %v", err)
	}

	if len(vals) != 1 {
		t.Fatalf("expected 1 value, got %d", len(vals))
	}
}
