package readcache

import (
	"bytes"
	"io"
	"os"
	"reflect"
	"runtime"
	"sync"
	"time"

	"github.com/influxdata/influxdb/toml"
	"github.com/shirou/gopsutil/v3/mem"
	"go.uber.org/zap"
)

const (
	DefaultReadMetaCachePercent = 3
	DefaultReadDataCachePercent = 10
	KB                          = 1024
	MB                          = 1024 * 1024
	GB                          = 1024 * 1024 * 1024
	maxMemUse                   = 64 * GB

	// Memory limits for ReadCache configuration (openGemini-compatible)
	MinMemoryLimit = 8 * GB   // 8GB minimum
	MaxMemoryLimit = 512 * GB // 512GB maximum
)

var (
	ReadMetaCachePct = DefaultReadMetaCachePercent
	ReadDataCachePct = DefaultReadDataCachePercent
)

// ReadCacheConfig represents the ReadCache configuration
type ReadCacheConfig struct {
	ReadPageSize       string    `toml:"read-page-size"`
	ReadMetaPageSize   []string  `toml:"read-meta-page-size"`
	ReadMetaCacheEn    toml.Size `toml:"enable-meta-cache"`
	ReadMetaCacheEnPct toml.Size `toml:"read-meta-cache-limit-pct"`
	ReadDataCacheEn    toml.Size `toml:"enable-data-cache"`
	ReadDataCacheEnPct toml.Size `toml:"read-data-cache-limit-pct"`
}

// NewReadCacheConfig creates a new ReadCache configuration with system memory-based defaults
func NewReadCacheConfig() ReadCacheConfig {
	size, _ := SysMem()
	memorySize := toml.Size(size * KB)

	return ReadCacheConfig{
		ReadPageSize:       "32kb",
		ReadMetaPageSize:   []string{},
		ReadMetaCacheEn:    toml.Size(1),
		ReadMetaCacheEnPct: toml.Size(DefaultReadMetaCachePercent),
		ReadDataCacheEn:    toml.Size(GetReadMetaCacheLimitSize(uint64(memorySize))),
		ReadDataCacheEnPct: toml.Size(DefaultReadDataCachePercent),
	}
}

func SetReadMetaCachePct(pct int) {
	if pct > 0 && pct < 100 {
		ReadMetaCachePct = pct
	}
}

func SetReadDataCachePct(pct int) {
	if pct > 0 && pct < 100 {
		ReadDataCachePct = pct
	}
}

func GetReadMetaCacheLimitSize(size uint64) uint64 {
	return size * uint64(ReadMetaCachePct) / 100
}

func GetReadDataCacheLimitSize(size uint64) uint64 {
	return size * uint64(ReadDataCachePct) / 100
}

// Memory detection variables
var (
	lastGetTime    time.Time
	sysMemTotal    int64
	sysMemFree     int64
	totalMemoryMax int64
	readMemMu      sync.Mutex
)

func init() {
	sysMemTotal, sysMemFree = ReadSysMemory()
	totalMemoryMax = sysMemTotal
	lastGetTime = time.Now()
}

// ReadSysMemory reads system memory information
func ReadSysMemory() (int64, int64) {
	if runtime.GOOS == "linux" {
		return ReadSysMemoryLinux()
	}
	info, _ := mem.VirtualMemory()
	return int64(info.Total / 1024), int64(info.Available / 1024)
}

// ReadSysMemoryLinux reads memory from /proc/meminfo (Linux-specific)
func ReadSysMemoryLinux() (int64, int64) {
	var buf [256]byte
	n1 := readSysMemInfo(buf[:])
	if n1 != 0 {
		totalStart := bytes.Index(buf[:], []byte("MemTotal:")) + len("MemTotal:")
		freeStart := bytes.Index(buf[totalStart:], []byte("MemAvailable:")) + len("MemAvailable:")
		memTotal := buf[totalStart:]
		memFree := buf[freeStart+totalStart:]
		end := bytes.Index(memTotal, []byte("kB"))
		memTotal = memTotal[:end]
		end = bytes.Index(memFree, []byte("kB"))
		memFree = memFree[:end]

		memTotal = bytes.TrimSpace(memTotal)
		memFree = bytes.TrimSpace(memFree)

		total_ := bytes2Int(memTotal)
		free_ := bytes2Int(memFree)

		return total_, free_
	}
	return maxMemUse, maxMemUse
}

// SysMem returns cached system memory with 100ms cache window
func SysMem() (total, free int64) {
	t := time.Now()
	readMemMu.Lock()
	defer readMemMu.Unlock()
	if t.Sub(lastGetTime) < 100*time.Millisecond {
		total, free = sysMemTotal, sysMemFree
		return
	}
	total, free = ReadSysMemory()
	if total <= 0 || free <= 0 {
		total, free = totalMemoryMax, totalMemoryMax
		return
	}
	lastGetTime = t
	sysMemTotal = total
	sysMemFree = free
	return
}

// readSysMemInfo reads /proc/meminfo into buffer
func readSysMemInfo(buf []byte) int {
	f, err := os.Open("/proc/meminfo")
	if err != nil {
		return 0
	}
	defer MustClose(f)

	n, err := f.ReadAt(buf, 0)
	if err != nil || n < len(buf) {
		return 0
	}
	return n
}

// bytes2Int converts byte slice to int64
func bytes2Int(b []byte) int64 {
	var v int64
	for _, c := range b {
		v = v*10 + int64(c-'0')
	}
	return v
}

// MemUsedPct returns the percentage of memory currently in use
func MemUsedPct() float64 {
	total, available := SysMem()
	return (1 - float64(available)/float64(total)) * 100
}

var logger *zap.Logger

func MustClose(obj io.Closer) {
	if obj == nil || IsObjectNil(obj) {
		return
	}

	err := obj.Close()
	if err != nil && logger != nil {
		logger.WithOptions(zap.AddCallerSkip(1)).
			Error("failed to close", zap.Error(err))
	}
}

func IsObjectNil(obj interface{}) bool {
	val := reflect.ValueOf(obj)
	switch val.Kind() {
	case reflect.Chan, reflect.Func, reflect.Map,
		reflect.UnsafePointer, reflect.Ptr,
		reflect.Interface, reflect.Slice:

		return val.IsNil()
	}

	return false
}

// ReadMetaCacheEn controls whether metadata cache is enabled
var ReadMetaCacheEn = false

// ReadDataCacheEn controls whether data cache is enabled
var ReadDataCacheEn = false

// EnableReadMetaCache enables or disables the metadata cache based on the size limit
func EnableReadMetaCache(en uint64) {
	if en > 0 {
		ReadMetaCacheEn = true
		SetReadMetaCacheLimitSize(en)
	} else {
		ReadMetaCacheEn = false
	}
}

// EnableReadDataCache enables or disables the data cache based on the size limit
func EnableReadDataCache(en uint64) {
	if en > 0 {
		ReadDataCacheEn = true
		SetReadDataCacheLimitSize(en)
	} else {
		ReadDataCacheEn = false
	}
}

// Uint64Limit applies min/max limits to a uint64 value (openGemini-compatible)
func Uint64Limit(min, max, value uint64) uint64 {
	if value < min {
		return min
	}
	if value > max {
		return max
	}
	return value
}
